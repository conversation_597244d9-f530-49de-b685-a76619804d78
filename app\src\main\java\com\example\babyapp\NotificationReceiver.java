package com.example.babyapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import androidx.core.app.NotificationCompat;

import java.util.List;

/**
 * Receives notification broadcasts and triggers notification display
 */
public class NotificationReceiver extends BroadcastReceiver {

    private static final String TAG = "NotificationReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            if (context == null) {
                Log.e(TAG, "Context is null in onReceive");
                return;
            }

            if (intent == null) {
                Log.e(TAG, "Intent is null in onReceive");
                return;
            }

            String action = intent.getAction();
            Log.d(TAG, "Received broadcast: " + action);

            if ("com.example.babyapp.NURSING_NOTIFICATION".equals(action)) {
                // Handle baby care notification
                int intervalMinutes = intent.getIntExtra("interval_minutes", 180);
                long entryId = intent.getLongExtra("entry_id", -1);
                showBabyCareNotification(context, intervalMinutes, entryId);

            } else if (Intent.ACTION_BOOT_COMPLETED.equals(action)) {
                // Handle device boot - reschedule notifications if needed
                Log.d(TAG, "Device boot completed, checking for notification rescheduling");
                rescheduleNotificationsAfterBoot(context);
            } else {
                Log.w(TAG, "Unknown action received: " + action);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in onReceive", e);
        }
    }

    /**
     * Show baby care notification to user as full-screen pop-up
     */
    private void showBabyCareNotification(Context context, int intervalMinutes, long entryId) {
        try {
            Log.d(TAG, "Showing full-screen baby care notification for interval: " + intervalMinutes + " minutes, entryId: " + entryId);

            if (context == null) {
                Log.e(TAG, "Context is null, cannot show notification");
                return;
            }

            // Create notification message
            String title = "Baby Care Reminder";
            String message = createNotificationMessage(intervalMinutes);

            // Check if app is in foreground - use different approach
            if (isAppInForeground(context)) {
                // App is in foreground - ONLY send local broadcast (NO system notification)
                Log.d(TAG, "App in foreground - sending ONLY local broadcast (no system notification)");
                sendInAppNotificationBroadcast(context, title, message, intervalMinutes, entryId);
            } else {
                // App is in background - show system notification AND try full-screen
                Log.d(TAG, "App in background - showing system notification");
                showBackgroundNotification(context, title, message, intervalMinutes, entryId);

                // Also try to launch full-screen activity for immediate attention
                if (canLaunchFullScreenActivity(context)) {
                    Intent fullScreenIntent = new Intent(context, FullScreenNotificationActivity.class);
                    fullScreenIntent.putExtra("title", title);
                    fullScreenIntent.putExtra("message", message);
                    fullScreenIntent.putExtra("interval_minutes", intervalMinutes);
                    fullScreenIntent.putExtra("entry_id", entryId);
                    fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK |
                                            Intent.FLAG_ACTIVITY_SINGLE_TOP);

                    try {
                        context.startActivity(fullScreenIntent);
                        Log.d(TAG, "Full-screen notification activity started successfully");
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to start full-screen notification activity", e);
                        // System notification is already shown above
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showBabyCareNotification", e);
            // Final fallback
            showFallbackNotification(context, "Baby Care Reminder", createNotificationMessage(intervalMinutes), intervalMinutes, entryId);
        }
    }

    /**
     * Check if we can safely launch the full-screen activity
     */
    private boolean canLaunchFullScreenActivity(Context context) {
        try {
            if (context == null) {
                Log.w(TAG, "Context is null, cannot launch full-screen activity");
                return false;
            }

            // Check if we have permission to show over other apps (Android 6.0+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!android.provider.Settings.canDrawOverlays(context)) {
                    Log.w(TAG, "No permission to draw over other apps");
                    return false;
                }
            }

            // For newer Android versions, be more conservative about background activity launches
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ has stricter background activity launch restrictions
                Log.d(TAG, "Android 10+ detected, using conservative approach for full-screen activity");

                // Check if we're in a state where background activity launch is allowed
                android.app.ActivityManager activityManager = (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
                if (activityManager != null) {
                    // Additional checks could be added here for specific conditions
                    Log.d(TAG, "ActivityManager available for background launch checks");
                }
            }

            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error checking if can launch full-screen activity", e);
            return false;
        }
    }

    /**
     * Show fallback notification using service (if full-screen fails)
     */
    private void showFallbackNotification(Context context, String title, String message, int intervalMinutes, long entryId) {
        try {
            Log.d(TAG, "Showing fallback notification service");

            Intent serviceIntent = new Intent(context, NotificationService.class);
            serviceIntent.putExtra("title", title);
            serviceIntent.putExtra("message", message);
            serviceIntent.putExtra("interval_minutes", intervalMinutes);
            serviceIntent.putExtra("entry_id", entryId);

            try {
                // Use regular service instead of foreground to avoid "noisy" notifications
                context.startService(serviceIntent);
                Log.d(TAG, "Fallback notification service started successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to start fallback notification service", e);
                // Final fallback - show notification directly
                showDirectNotification(context, title, message);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showFallbackNotification", e);
        }
    }

    /**
     * Show background notification using BackgroundNotificationService
     */
    private void showBackgroundNotification(Context context, String title, String message, int intervalMinutes, long entryId) {
        try {
            Log.d(TAG, "Starting background notification service");

            Intent backgroundServiceIntent = new Intent(context, BackgroundNotificationService.class);
            backgroundServiceIntent.putExtra("title", title);
            backgroundServiceIntent.putExtra("message", message);
            backgroundServiceIntent.putExtra("interval_minutes", intervalMinutes);
            backgroundServiceIntent.putExtra("entry_id", entryId);
            backgroundServiceIntent.putExtra("show_full_screen", false); // Don't duplicate full-screen

            try {
                // Use regular service instead of foreground to avoid "noisy" notifications
                context.startService(backgroundServiceIntent);
                Log.d(TAG, "Background notification service started successfully");
            } catch (Exception e) {
                Log.e(TAG, "Failed to start background notification service", e);
                // Final fallback to regular notification service
                showFallbackNotification(context, title, message, intervalMinutes, entryId);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showBackgroundNotification", e);
        }
    }

    /**
     * Send local broadcast to show notification within the app
     */
    private void sendInAppNotificationBroadcast(Context context, String title, String message, int intervalMinutes, long entryId) {
        try {
            Intent localIntent = new Intent("com.example.babyapp.IN_APP_NOTIFICATION");
            localIntent.putExtra("title", title);
            localIntent.putExtra("message", message);
            localIntent.putExtra("interval_minutes", intervalMinutes);
            localIntent.putExtra("entry_id", entryId);

            // Use LocalBroadcastManager to send to app components only
            androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(context)
                .sendBroadcast(localIntent);

            Log.d(TAG, "In-app notification broadcast sent successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error sending in-app notification broadcast", e);
            // Fallback to background notification
            showBackgroundNotification(context, title, message, intervalMinutes, entryId);
        }
    }

    /**
     * Check if the app is currently in foreground
     */
    private boolean isAppInForeground(Context context) {
        try {
            android.app.ActivityManager activityManager = (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                java.util.List<android.app.ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();
                if (runningProcesses != null) {
                    for (android.app.ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                        if (processInfo.processName.equals(context.getPackageName())) {
                            boolean isInForeground = processInfo.importance == android.app.ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND;
                            Log.d(TAG, "App process importance: " + processInfo.importance + " (foreground=" + isInForeground + ")");
                            return isInForeground;
                        }
                    }
                }
            }
            Log.w(TAG, "Could not determine app foreground status, assuming background");
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking app foreground status", e);
            return false;
        }
    }

    /**
     * Create appropriate notification message based on interval
     */
    private String createNotificationMessage(int intervalMinutes) {
        if (intervalMinutes < 60) {
            return "It's been " + intervalMinutes + " minutes since the last activity. Time to check on your baby!";
        } else {
            int hours = intervalMinutes / 60;
            int minutes = intervalMinutes % 60;

            if (minutes == 0) {
                if (hours == 1) {
                    return "It's been 1 hour since the last activity. Time to check on your baby!";
                } else {
                    return "It's been " + hours + " hours since the last activity. Time to check on your baby!";
                }
            } else {
                if (hours == 1) {
                    return "It's been 1 hour and " + minutes + " minutes since the last activity. Time to check on your baby!";
                } else {
                    return "It's been " + hours + " hours and " + minutes + " minutes since the last activity. Time to check on your baby!";
                }
            }
        }
    }

    /**
     * Reschedule notifications after device boot
     */
    private void rescheduleNotificationsAfterBoot(Context context) {
        try {
            if (context == null) {
                Log.e(TAG, "Context is null, cannot reschedule notifications");
                return;
            }

            com.example.babyapp.NotificationManager notificationManager = new com.example.babyapp.NotificationManager(context);

            if (notificationManager.isNotificationEnabled()) {
                // Use new system: check active notifications instead of last nursing time
                List<com.example.babyapp.NotificationManager.NotificationInfo> scheduledNotifications =
                    notificationManager.getScheduledNotifications();

                if (!scheduledNotifications.isEmpty()) {
                    Log.d(TAG, "Found " + scheduledNotifications.size() + " active notifications to reschedule");

                    // Check if any notifications were missed while device was off
                    long currentTime = System.currentTimeMillis();
                    boolean foundMissedNotification = false;

                    for (com.example.babyapp.NotificationManager.NotificationInfo notification : scheduledNotifications) {
                        if (notification.scheduledTimeMillis <= currentTime) {
                            // This notification was missed
                            Log.d(TAG, "Missed notification detected for entry " + notification.id);
                            if (!foundMissedNotification) {
                                // Show only one missed notification popup
                                showBabyCareNotification(context, 180, -1); // Default interval, no specific entry ID
                                foundMissedNotification = true;
                            }
                        }
                    }

                    if (!foundMissedNotification) {
                        Log.d(TAG, "All notifications are still in the future, no action needed");
                    }
                } else {
                    Log.d(TAG, "No active notifications found, nothing to reschedule");
                }
            } else {
                Log.d(TAG, "Notifications disabled, not rescheduling");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error rescheduling notifications after boot", e);
        }
    }

    /**
     * Show notification directly without using service (fallback method)
     */
    private void showDirectNotification(Context context, String title, String message) {
        try {
            Log.d(TAG, "Showing direct notification as fallback");

            // Record that notification was sent for missed notification tracking
            try {
                MissedNotificationTracker tracker = new MissedNotificationTracker(context);
                tracker.recordNotificationSent(System.currentTimeMillis());
            } catch (Exception e) {
                Log.e(TAG, "Error recording notification for missed tracking", e);
                // Continue with notification display even if tracking fails
            }

            // Create notification channel if needed
            createNotificationChannelIfNeeded(context);

            // Create intent to open the app
            // Use flags that bring existing app to front instead of creating new instance
            Intent mainIntent = new Intent(context, MainActivity.class);
            mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            mainIntent.putExtra("opened_from_notification", true); // Flag to clear badge

            PendingIntent pendingIntent = PendingIntent.getActivity(
                context,
                0,
                mainIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            // Build enhanced notification for background scenarios
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, "nursing_notifications")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle(title != null ? title : "Baby Care Reminder")
                .setContentText(message != null ? message : "Time to check on your baby!")
                .setStyle(new NotificationCompat.BigTextStyle().bigText(message != null ? message : "Time to check on your baby!"))
                .setPriority(NotificationCompat.PRIORITY_MAX) // Maximum priority for critical baby care
                .setCategory(NotificationCompat.CATEGORY_ALARM) // Use ALARM category for better visibility
                .setAutoCancel(true)
                .setOngoing(false)
                .setShowWhen(true)
                .setWhen(System.currentTimeMillis())
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // Show on lock screen
                .setLights(0xFF0000FF, 1000, 1000) // Blue light blinking
                .setContentIntent(pendingIntent)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

            // Show notification
            android.app.NotificationManager notificationManager = (android.app.NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            if (notificationManager != null) {
                notificationManager.notify(1002, builder.build());
                Log.d(TAG, "Direct notification shown successfully");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error showing direct notification", e);
        }
    }

    /**
     * Create notification channel if needed (for direct notifications)
     */
    private void createNotificationChannelIfNeeded(Context context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                android.app.NotificationManager notificationManager = (android.app.NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                if (notificationManager != null) {
                    NotificationChannel channel = new NotificationChannel(
                        "nursing_notifications",
                        "Nursing Reminders",
                        android.app.NotificationManager.IMPORTANCE_HIGH
                    );
                    channel.setDescription("Notifications for nursing reminders");
                    notificationManager.createNotificationChannel(channel);
                    Log.d(TAG, "Notification channel created for direct notification");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error creating notification channel for direct notification", e);
        }
    }
}
