package com.example.babyapp;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Base activity class that handles locale management for all activities
 * All activities should extend this class to ensure consistent language support
 */
public abstract class BaseActivity extends AppCompatActivity {
    
    private static final String TAG = "BaseActivity";
    
    @Override
    protected void attachBaseContext(Context newBase) {
        // Apply saved language before activity creation
        String savedLanguage = LocaleManager.getSavedLanguage(newBase);
        Context updatedContext = LocaleManager.updateBaseContextLocale(newBase, savedLanguage);
        super.attachBaseContext(updatedContext);

        Log.d(TAG, "BaseActivity attachBaseContext - Language: " + savedLanguage + " (no cache)");
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Ensure locale is properly applied
        String savedLanguage = LocaleManager.getSavedLanguage(this);
        LocaleManager.setLocale(this, savedLanguage);

        Log.d(TAG, "BaseActivity onCreate - Applied language: " + savedLanguage + " (Activity: " + this.getClass().getSimpleName() + ")");
    }
    
    @Override
    protected void onResume() {
        super.onResume();

        // Reapply locale in case it was changed in settings
        String savedLanguage = LocaleManager.getSavedLanguage(this);
        Log.d(TAG, "BaseActivity onResume - Current language: " + savedLanguage + " (Activity: " + this.getClass().getSimpleName() + ")");

        // Check if language has changed and recreate if needed
        checkAndHandleLanguageChange(savedLanguage);
    }

    /**
     * Check if language has changed and recreate activity if needed
     */
    private void checkAndHandleLanguageChange(String currentLanguage) {
        // Get the locale that was used when this activity was created
        String activityLocale = getResources().getConfiguration().getLocales().get(0).getLanguage();
        String expectedLocale = LocaleManager.getLanguageCode(currentLanguage);

        Log.d(TAG, "Language check - Activity locale: " + activityLocale + ", Expected: " + expectedLocale);

        // If locales don't match, recreate the activity
        if (!activityLocale.equals(expectedLocale)) {
            Log.d(TAG, "Language mismatch detected - recreating activity: " + this.getClass().getSimpleName());
            recreate();
        }
    }
}
