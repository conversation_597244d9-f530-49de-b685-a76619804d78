<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_blue"
    tools:context=".AddEntryActivity">

    <!-- Top Bar -->
    <LinearLayout
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/baby_pink"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/backButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/back_button"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/white" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/add_entry_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- PDF Export Button -->
        <ImageView
            android:id="@+id/pdfExportIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/pdf_export_title"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_pdf_export"
            app:tint="@color/white" />

    </LinearLayout>

    <!-- Scrollable Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Dummy focusable view to prevent auto-focus on EditText -->
            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:focusable="true"
                android:focusableInTouchMode="true" />

            <!-- Time Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/time_section"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Time Card Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/edittext_background"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Date Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/selectDateIcon"
                        android:layout_width="40dp"
                        android:layout_height="32dp"
                        android:background="@drawable/icon_button_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:shadowColor="@color/baby_pink"
                        android:shadowDx="2"
                        android:shadowDy="2"
                        android:shadowRadius="4"
                        android:text="📅"
                        android:textSize="16sp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:text="@string/date_label"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp"
                        android:gravity="start"
                        android:layout_marginEnd="4dp" />

                    <!-- Date Input Fields -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/dayEditText"
                            android:layout_width="40dp"
                            android:layout_height="20dp"
                            android:background="@drawable/edittext_background"
                            android:gravity="center"
                            android:hint="15"
                            android:inputType="number"
                            android:maxLength="2"
                            android:textColor="@color/dark_blue"
                            android:textColorHint="@color/light_gray"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="4dp"
                            android:text="/"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/monthEditText"
                            android:layout_width="40dp"
                            android:layout_height="20dp"
                            android:background="@drawable/edittext_background"
                            android:gravity="center"
                            android:hint="12"
                            android:inputType="number"
                            android:maxLength="2"
                            android:textColor="@color/dark_blue"
                            android:textColorHint="@color/light_gray"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="4dp"
                            android:text="/"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/yearEditText"
                            android:layout_width="60dp"
                            android:layout_height="20dp"
                            android:background="@drawable/edittext_background"
                            android:gravity="center"
                            android:hint="2024"
                            android:inputType="number"
                            android:maxLength="4"
                            android:textColor="@color/dark_blue"
                            android:textColorHint="@color/light_gray"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Time Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/selectTimeIcon"
                        android:layout_width="40dp"
                        android:layout_height="32dp"
                        android:background="@drawable/icon_button_background"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:shadowColor="@color/baby_pink"
                        android:shadowDx="2"
                        android:shadowDy="2"
                        android:shadowRadius="4"
                        android:text="🕐"
                        android:textSize="16sp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="wrap_content"
                        android:text="@string/time_label"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp"
                        android:gravity="start"
                        android:layout_marginEnd="4dp" />

                    <EditText
                        android:id="@+id/hoursEditText"
                        android:layout_width="40dp"
                        android:layout_height="20dp"
                        android:background="@drawable/edittext_background"
                        android:gravity="center"
                        android:hint="14"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textColor="@color/dark_blue"
                        android:textColorHint="@color/light_gray"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp"
                        android:text="h"
                        android:textColor="@color/gray"
                        android:textSize="12sp" />

                    <EditText
                        android:id="@+id/minutesEditText"
                        android:layout_width="40dp"
                        android:layout_height="20dp"
                        android:background="@drawable/edittext_background"
                        android:gravity="center"
                        android:hint="30"
                        android:inputType="number"
                        android:maxLength="2"
                        android:textColor="@color/dark_blue"
                        android:textColorHint="@color/light_gray"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp"
                        android:text="m"
                        android:textColor="@color/gray"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Diaper Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/diaper_section"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Diaper Card Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/edittext_background"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Poop Row -->
                <CheckBox
                    android:id="@+id/poopCheckbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="@string/poop_label"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink"
                    android:button="@drawable/checkbox_selector" />

                <!-- Pee Row -->
                <CheckBox
                    android:id="@+id/peeCheckbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/pee_label"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink"
                    android:button="@drawable/checkbox_selector" />

            </LinearLayout>

            <!-- Formula Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/formula_section"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Formula Card Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/edittext_background"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Formula Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/formulaCheckbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/formula_feeding_label"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp"
                        android:buttonTint="@color/baby_pink"
                        android:button="@drawable/checkbox_selector" />

                    <LinearLayout
                        android:id="@+id/formulaAmountLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="15dp"
                        android:gravity="center_vertical|end"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <EditText
                            android:id="@+id/formulaAmountEditText"
                            android:layout_width="60dp"
                            android:layout_height="20dp"
                            android:background="@drawable/edittext_background"
                            android:gravity="center"
                            android:hint="150"
                            android:inputType="number"
                            android:maxLength="4"
                            android:textColor="@color/dark_blue"
                            android:textColorHint="@color/light_gray"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:text="ml"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Nursing Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/nursing_section"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Nursing Card Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/edittext_background"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Left Breast Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/leftBreastCheckbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/left_breast_label"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp"
                        android:buttonTint="@color/baby_pink"
                        android:button="@drawable/checkbox_selector" />

                    <LinearLayout
                        android:id="@+id/leftBreastTimeLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_vertical|end"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <EditText
                            android:id="@+id/leftBreastTimeEditText"
                            android:layout_width="60dp"
                            android:layout_height="20dp"
                            android:background="@drawable/edittext_background"
                            android:gravity="center"
                            android:hint="10"
                            android:inputType="number"
                            android:maxLength="3"
                            android:textColor="@color/dark_blue"
                            android:textColorHint="@color/light_gray"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="8dp"
                            android:text="min"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/leftBreastTimeDisplay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:background="@color/baby_pink"
                            android:paddingStart="8dp"
                            android:paddingTop="4dp"
                            android:paddingEnd="8dp"
                            android:paddingBottom="4dp"
                            android:text=""
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Right Breast Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <CheckBox
                        android:id="@+id/rightBreastCheckbox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/right_breast_label"
                        android:textColor="@color/dark_blue"
                        android:textSize="14sp"
                        android:buttonTint="@color/baby_pink"
                        android:button="@drawable/checkbox_selector" />

                    <LinearLayout
                        android:id="@+id/rightBreastTimeLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_vertical|end"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <EditText
                            android:id="@+id/rightBreastTimeEditText"
                            android:layout_width="60dp"
                            android:layout_height="20dp"
                            android:background="@drawable/edittext_background"
                            android:gravity="center"
                            android:hint="10"
                            android:inputType="number"
                            android:maxLength="3"
                            android:textColor="@color/dark_blue"
                            android:textColorHint="@color/light_gray"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="8dp"
                            android:text="min"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/rightBreastTimeDisplay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:background="@color/baby_pink"
                            android:paddingStart="8dp"
                            android:paddingTop="4dp"
                            android:paddingEnd="8dp"
                            android:paddingBottom="4dp"
                            android:text=""
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- Notification Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/notifications_section"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Notification Card Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/edittext_background"
                android:orientation="vertical"
                android:padding="16dp">

                <CheckBox
                    android:id="@+id/nursingNotificationCheckbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/notification_description"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink"
                    android:button="@drawable/checkbox_selector" />

            </LinearLayout>

            <!-- Burping Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/burping_section"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Burping Card Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/edittext_background"
                android:orientation="vertical"
                android:padding="16dp">

                <CheckBox
                    android:id="@+id/burpingCheckbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/burping_label"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink"
                    android:button="@drawable/checkbox_selector" />

            </LinearLayout>

            <!-- Description Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/description_section"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Description Card Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:background="@drawable/edittext_background"
                android:orientation="vertical"
                android:padding="16dp">

                <EditText
                    android:id="@+id/descriptionEditText"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:background="@android:color/transparent"
                    android:gravity="top|start"
                    android:hint="@string/description_hint_add_entry"
                    android:inputType="textMultiLine"
                    android:textColor="@color/dark_blue"
                    android:textColorHint="@color/gray"
                    android:textSize="14sp" />

            </LinearLayout>



        </LinearLayout>

    </ScrollView>

    <!-- Save Button -->
    <Button
        android:id="@+id/saveButton"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_margin="16dp"
        android:backgroundTint="@color/dark_blue"
        android:text="@string/save_entry_button"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold" />

</LinearLayout>
