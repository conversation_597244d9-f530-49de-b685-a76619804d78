<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_blue"
    android:orientation="vertical"
    tools:context=".SettingsActivity">

    <!-- Top Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/baby_pink"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/backButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/selectable_background"
            android:clickable="true"
            android:contentDescription="Back"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_back"
            app:tint="@color/white" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/settings_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Spacer for balance -->
        <View
            android:layout_width="40dp"
            android:layout_height="40dp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">

            <!-- First Name -->
            <TextView
                android:id="@+id/firstNameLabel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/first_name_label"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/firstNameEdit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:hint="@string/first_name_hint"
            android:inputType="textPersonName"
            android:padding="12dp"
            android:textColor="@color/dark_blue"
            android:textColorHint="@color/dark_blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/firstNameLabel" />

        <!-- Last Name -->
        <TextView
            android:id="@+id/lastNameLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/last_name_label"
            android:textColor="@color/dark_blue"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/firstNameEdit" />

        <EditText
            android:id="@+id/lastNameEdit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:hint="@string/last_name_hint"
            android:inputType="textPersonName"
            android:padding="12dp"
            android:textColor="@color/dark_blue"
            android:textColorHint="@color/dark_blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lastNameLabel" />

        <!-- Email -->
        <TextView
            android:id="@+id/emailLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/email_label"
            android:textColor="@color/dark_blue"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lastNameEdit" />

        <EditText
            android:id="@+id/emailEdit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:hint="@string/email_hint"
            android:inputType="textEmailAddress"
            android:padding="12dp"
            android:textColor="@color/dark_blue"
            android:textColorHint="@color/dark_blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/emailLabel" />

        <!-- Email Error Label -->
        <TextView
            android:id="@+id/emailErrorLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/invalid_email_message"
            android:textColor="#FF5722"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/emailEdit" />

        <!-- Time Format -->
        <TextView
            android:id="@+id/timeFormatLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/time_format_label"
            android:textColor="@color/dark_blue"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/emailErrorLabel" />

        <LinearLayout
            android:id="@+id/timeFormatLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/timeFormatLabel">

            <TextView
                android:id="@+id/timeFormatText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/time_format_switch_label_12h"
                android:textColor="@color/dark_blue"
                android:textSize="14sp" />

            <Switch
                android:id="@+id/timeFormatSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:thumbTint="@color/baby_pink"
                android:trackTint="@color/light_pink" />

        </LinearLayout>

        <!-- Time Format Description -->
        <TextView
            android:id="@+id/timeFormatDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/time_format_example_12h"
            android:textColor="@color/gray"
            android:textSize="12sp"
            android:textStyle="italic"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/timeFormatLayout" />

        <!-- Language -->
        <TextView
            android:id="@+id/languageLabel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/language_label"
            android:textColor="@color/dark_blue"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/timeFormatDescription" />

        <Spinner
            android:id="@+id/languageSpinner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:padding="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/languageLabel" />

            <!-- Save Button -->
            <Button
                android:id="@+id/saveButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:backgroundTint="@color/baby_pink"
                android:text="@string/save_button"
                android:textColor="@color/white"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/languageSpinner" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <!-- Loading Overlay for Language Change -->
    <LinearLayout
        android:id="@+id/loadingOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/edittext_background"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp">

            <ProgressBar
                android:id="@+id/loadingProgressBar"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp"
                android:indeterminateTint="@color/baby_pink" />

            <TextView
                android:id="@+id/loadingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/changing_language"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/please_wait"
                android:textColor="@color/gray"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
