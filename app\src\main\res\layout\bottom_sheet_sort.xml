<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/edittext_background">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/sort_by_title"
        android:textColor="@color/dark_blue"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Date Sort Option -->
    <LinearLayout
        android:id="@+id/sortByDateLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/sort_by_date"
            android:textColor="@color/dark_blue"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/dateSortIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_up"
            app:tint="@color/dark_blue"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Time Sort Option -->
    <LinearLayout
        android:id="@+id/sortByTimeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:layout_marginTop="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/sort_by_time"
            android:textColor="@color/dark_blue"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/timeSortIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_up"
            app:tint="@color/dark_blue"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
