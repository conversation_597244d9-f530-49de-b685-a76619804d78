package com.example.babyapp;

import androidx.appcompat.app.AppCompatActivity;
import androidx.annotation.NonNull;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.util.Patterns;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import android.os.Handler;

/**
 * Settings activity for user profile management
 * Features: User profile form, time format toggle, language selection, form validation
 * Extends BaseActivity for automatic locale management
 */
public class SettingsActivity extends BaseActivity {

    // Constants
    private static final String PREFS_NAME = "BabyAppSettings";
    private static final String PREF_FIRST_NAME = "first_name";
    private static final String PREF_LAST_NAME = "last_name";
    private static final String PREF_EMAIL = "email";
    private static final String PREF_LANGUAGE = "language";

    // UI Components
    private EditText firstNameEdit;
    private EditText lastNameEdit;
    private EditText emailEdit;
    private LinearLayout timeFormatLayout;
    private TextView timeFormatText;
    private Switch timeFormatSwitch;
    private Spinner languageSpinner;
    private Button saveButton;
    private ImageView backButton;
    private TextView emailErrorLabel;
    private LinearLayout loadingOverlay;
    private ProgressBar loadingProgressBar;
    private TextView loadingText;
    private TextView timeFormatDescription;

    // Data
    private SharedPreferences sharedPreferences;
    private TimeFormatManager timeFormatManager;

    // Language switching control
    private boolean isLoadingSettings = false;
    private String pendingLanguageChange = null;

    // Time format switching control
    private Boolean pendingTimeFormatChange = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        initializeComponents();
        setupLanguageSpinner();
        setupClickListeners();
        loadSavedSettings();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d("SettingsActivity", "onResume called - ensuring language switching is enabled");

        // Ensure loading flag is reset (don't reload settings - that triggers spinner)
        isLoadingSettings = false;

        // Log current state for debugging
        String currentLanguage = LocaleManager.getSavedLanguage(this);
        Log.d("SettingsActivity", "Current language: " + currentLanguage + ", isLoadingSettings: " + isLoadingSettings);
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d("SettingsActivity", "onPause called");
    }

    /**
     * Initialize SharedPreferences and UI components
     */
    private void initializeComponents() {
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        timeFormatManager = new TimeFormatManager(this);
        initializeViews();
    }

    /**
     * Initialize all UI components
     */
    private void initializeViews() {
        firstNameEdit = findViewById(R.id.firstNameEdit);
        lastNameEdit = findViewById(R.id.lastNameEdit);
        emailEdit = findViewById(R.id.emailEdit);
        timeFormatLayout = findViewById(R.id.timeFormatLayout);
        timeFormatText = findViewById(R.id.timeFormatText);
        timeFormatSwitch = findViewById(R.id.timeFormatSwitch);
        languageSpinner = findViewById(R.id.languageSpinner);
        saveButton = findViewById(R.id.saveButton);
        backButton = findViewById(R.id.backButton);
        emailErrorLabel = findViewById(R.id.emailErrorLabel);
        loadingOverlay = findViewById(R.id.loadingOverlay);
        loadingProgressBar = findViewById(R.id.loadingProgressBar);
        loadingText = findViewById(R.id.loadingText);
        timeFormatDescription = findViewById(R.id.timeFormatDescription);
    }

    /**
     * Set up language spinner with available options and custom styling
     */
    private void setupLanguageSpinner() {
        String[] languages = getResources().getStringArray(R.array.language_options);
        ArrayAdapter<String> adapter = new ArrayAdapter<>(
            this,
            R.layout.spinner_item_custom,
            languages
        );
        adapter.setDropDownViewResource(R.layout.spinner_dropdown_item_custom);
        languageSpinner.setAdapter(adapter);
    }

    /**
     * Set up click listeners for buttons and language spinner
     */
    private void setupClickListeners() {
        // Save button with lambda expression
        saveButton.setOnClickListener(v -> saveSettings());

        // Back button with lambda expression
        backButton.setOnClickListener(v -> finish());

        // Time format switch - save on button click (no immediate switching)
        timeFormatSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            // Skip if we're loading settings
            if (isLoadingSettings) {
                Log.d("SettingsActivity", "Skipping time format change - loading settings");
                return;
            }

            boolean currentFormat = timeFormatManager.is24HourFormat();
            Log.d("SettingsActivity", "Time format changed: " + isChecked + ", Current: " + currentFormat);

            // Update text immediately for better UX
            updateTimeFormatText(isChecked);

            // Store pending change (don't apply immediately)
            if (isChecked != currentFormat) {
                pendingTimeFormatChange = isChecked;
                Log.d("SettingsActivity", "Pending time format change: " + pendingTimeFormatChange);
            } else {
                pendingTimeFormatChange = null;
                Log.d("SettingsActivity", "No time format change needed");
            }
        });

        // Language spinner - save on button click (no immediate switching)
        languageSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Log.d("SettingsActivity", "onItemSelected called - position: " + position + ", isLoadingSettings: " + isLoadingSettings);

                // Skip if we're loading settings (prevents recursive calls during recreate)
                if (isLoadingSettings) {
                    Log.d("SettingsActivity", "Skipping language selection - loading settings");
                    return;
                }

                String selectedLanguage = parent.getItemAtPosition(position).toString();
                String currentLanguage = LocaleManager.getSavedLanguage(SettingsActivity.this);

                Log.d("SettingsActivity", "Language selected: '" + selectedLanguage + "', Current: '" + currentLanguage + "'");

                // Store pending change (don't apply immediately)
                if (!selectedLanguage.equals(currentLanguage)) {
                    pendingLanguageChange = selectedLanguage;
                    Log.d("SettingsActivity", "Pending language change: " + pendingLanguageChange);
                } else {
                    pendingLanguageChange = null;
                    Log.d("SettingsActivity", "No language change needed");
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                // Do nothing
            }
        });
    }

    /**
     * Load previously saved settings from SharedPreferences
     */
    private void loadSavedSettings() {
        Log.d("SettingsActivity", "loadSavedSettings() called");

        // Set flag to prevent language switching during loading
        isLoadingSettings = true;

        try {
            firstNameEdit.setText(sharedPreferences.getString(PREF_FIRST_NAME, ""));
            lastNameEdit.setText(sharedPreferences.getString(PREF_LAST_NAME, ""));
            emailEdit.setText(sharedPreferences.getString(PREF_EMAIL, ""));

            // Update time format UI
            timeFormatSwitch.setChecked(timeFormatManager.is24HourFormat());
            updateTimeFormatText(timeFormatManager.is24HourFormat());

            // Set language spinner selection
            String savedLanguage = LocaleManager.getSavedLanguage(this); // Use LocaleManager instead of direct SharedPreferences
            String[] languages = getResources().getStringArray(R.array.language_options);

            Log.d("SettingsActivity", "Loading saved language: " + savedLanguage);

            boolean languageFound = false;
            for (int i = 0; i < languages.length; i++) {
                if (languages[i].equals(savedLanguage)) {
                    Log.d("SettingsActivity", "Setting spinner to position " + i + " (" + languages[i] + ")");
                    languageSpinner.setSelection(i);
                    languageFound = true;
                    break;
                }
            }

            if (!languageFound) {
                Log.w("SettingsActivity", "Saved language '" + savedLanguage + "' not found in options, defaulting to English");
                languageSpinner.setSelection(0); // Default to English
            }

        } catch (Exception e) {
            Log.e("SettingsActivity", "Error loading settings", e);
        } finally {
            // Always clear flag after loading is complete
            isLoadingSettings = false;
            Log.d("SettingsActivity", "loadSavedSettings() completed, isLoadingSettings = false");
        }
    }

    /**
     * Validate and save settings to SharedPreferences
     */
    private void saveSettings() {
        // Get values from form fields
        String firstName = firstNameEdit.getText().toString().trim();
        String lastName = lastNameEdit.getText().toString().trim();
        String email = emailEdit.getText().toString().trim();
        boolean is24HourFormat = timeFormatSwitch.isChecked();
        String selectedLanguage = languageSpinner.getSelectedItem().toString();

        // Clear previous error messages
        emailErrorLabel.setVisibility(View.GONE);

        boolean hasErrors = false;

        // Validate email format only if email is provided
        if (!TextUtils.isEmpty(email) && !isValidEmail(email)) {
            emailErrorLabel.setVisibility(View.VISIBLE);
            Log.w("SettingsActivity", "Invalid email format: " + email);
            hasErrors = true;
        }

        // If there are any errors, don't save
        if (hasErrors) {
            return;
        }

        // Apply pending time format change if any
        boolean finalTimeFormat = is24HourFormat;
        boolean timeFormatChanged = false;

        if (pendingTimeFormatChange != null) {
            Log.d("SettingsActivity", "Applying pending time format change: " + pendingTimeFormatChange);
            timeFormatManager.setTimeFormat(pendingTimeFormatChange);
            finalTimeFormat = pendingTimeFormatChange;
            timeFormatChanged = true;
            pendingTimeFormatChange = null;
        }

        // Apply pending language change if any
        String finalLanguage = selectedLanguage;
        boolean languageChanged = false;

        if (pendingLanguageChange != null) {
            Log.d("SettingsActivity", "Applying pending language change: " + pendingLanguageChange);
            LocaleManager.setLocale(this, pendingLanguageChange);
            finalLanguage = pendingLanguageChange;
            languageChanged = true;
            pendingLanguageChange = null;
        }

        // Save to SharedPreferences
        saveToPreferences(firstName, lastName, email, finalTimeFormat, finalLanguage);

        // Show success message
        showSuccessMessage(firstName, lastName, email, finalTimeFormat, finalLanguage);

        // If language was changed, show loading and restart app
        if (languageChanged) {
            Log.d("SettingsActivity", "Language changed - showing loading and restarting app");
            showLanguageChangeLoading();
            return; // Don't finish() if restarting
        }

        // If time format was changed, show loading and restart app
        if (timeFormatChanged) {
            Log.d("SettingsActivity", "Time format changed - showing loading and restarting app");
            showTimeFormatChangeLoading();
            return; // Don't finish() if restarting
        }

        // Close settings activity
        finish();
    }

    /**
     * Validate email format using Android's built-in pattern
     */
    private boolean isValidEmail(@NonNull String email) {
        return Patterns.EMAIL_ADDRESS.matcher(email).matches();
    }



    /**
     * Save settings to SharedPreferences
     */
    private void saveToPreferences(@NonNull String firstName, @NonNull String lastName,
                                 @NonNull String email, boolean is24HourFormat, @NonNull String language) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(PREF_FIRST_NAME, firstName);
        editor.putString(PREF_LAST_NAME, lastName);
        editor.putString(PREF_EMAIL, email);
        // Save time format using TimeFormatManager
        timeFormatManager.setTimeFormat(is24HourFormat);
        editor.putString(PREF_LANGUAGE, language);
        editor.apply();
    }

    /**
     * Log success message with saved settings
     */
    private void showSuccessMessage(@NonNull String firstName, @NonNull String lastName,
                                  @NonNull String email, boolean is24HourFormat, @NonNull String language) {
        String timeFormat = is24HourFormat ? "24-hour" : "12-hour";
        String message = String.format(
            "Settings saved!\nName: %s %s\nEmail: %s\nTime Format: %s\nLanguage: %s",
            firstName, lastName, email, timeFormat, language
        );
        Log.d("SettingsActivity", message);
    }

    /**
     * Show loading overlay with smooth animation and start language change process
     */
    private void showLanguageChangeLoading() {
        Log.d("SettingsActivity", "Showing language change loading");

        // Set loading text for language change
        loadingText.setText(getString(R.string.changing_language));

        // Show loading overlay (no toast message here)
        loadingOverlay.setVisibility(View.VISIBLE);
        loadingOverlay.setAlpha(0f);
        loadingOverlay.animate()
            .alpha(1f)
            .setDuration(300)
            .start();

        // Disable save button to prevent multiple clicks
        saveButton.setEnabled(false);
        saveButton.setAlpha(0.5f);

        // Start the language change process after a short delay for smooth UX
        new Handler().postDelayed(() -> {
            restartApp("language_changed");
        }, 800); // Give user time to see the loading state
    }

    /**
     * Show loading overlay with smooth animation and start time format change process
     */
    private void showTimeFormatChangeLoading() {
        Log.d("SettingsActivity", "Showing time format change loading");

        // Set loading text for time format change
        loadingText.setText(getString(R.string.changing_time_format));

        // Show loading overlay (no toast message here)
        loadingOverlay.setVisibility(View.VISIBLE);
        loadingOverlay.setAlpha(0f);
        loadingOverlay.animate()
            .alpha(1f)
            .setDuration(300)
            .start();

        // Disable save button to prevent multiple clicks
        saveButton.setEnabled(false);
        saveButton.setAlpha(0.5f);

        // Start the time format change process after a short delay for smooth UX
        new Handler().postDelayed(() -> {
            restartApp("time_format_changed");
        }, 800); // Give user time to see the loading state
    }

    /**
     * Apply changes and restart entire application
     */
    private void restartApp(String changeType) {
        Log.d("SettingsActivity", "Restarting entire application for " + changeType);

        // Force update application locale if language changed
        if ("language_changed".equals(changeType)) {
            LocaleManager.forceUpdateApplicationLocale(this);
        }

        // Clear the entire activity stack and start fresh with MainActivity
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);

        // Add extra to indicate what changed
        intent.putExtra(changeType, true);

        startActivity(intent);

        // Finish current activity
        finish();

        // Optional: Force process restart for complete locale refresh
        // Uncomment the lines below if BaseActivity recreation doesn't work properly
        /*
        new Handler().postDelayed(() -> {
            System.exit(0);
        }, 100);
        */
    }

    /**
     * Update time format text based on current selection
     */
    private void updateTimeFormatText(boolean is24Hour) {
        try {
            // Update main text
            if (timeFormatText != null) {
                String mainText = is24Hour ?
                    getString(R.string.time_format_switch_label_24h) :
                    getString(R.string.time_format_switch_label_12h);
                timeFormatText.setText(mainText);
                Log.d("SettingsActivity", "Updated time format text: " + mainText);
            }

            // Update description
            if (timeFormatDescription != null) {
                String descriptionText = is24Hour ?
                    getString(R.string.time_format_example_24h) :
                    getString(R.string.time_format_example_12h);
                timeFormatDescription.setText(descriptionText);
                Log.d("SettingsActivity", "Updated time format description: " + descriptionText);
            }
        } catch (Exception e) {
            Log.e("SettingsActivity", "Error updating time format text", e);
        }
    }
}
