package com.example.babyapp;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.TimePicker;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

/**
 * Activity for adding new baby care entries
 * Includes time selection, diaper tracking, nursing tracking, and description
 * Extends BaseActivity for automatic locale management
 */
public class AddEntryActivity extends BaseActivity implements TimePickerDialog.OnTimeSetListener, DatePickerDialog.OnDateSetListener, PDFExportDialog.PDFExportListener {

    // Constants
    private static final String TAG = "AddEntryActivity";
    private static final String TIME_FORMAT_PATTERN = "HH:mm";

    // UI Components
    private ImageView backButton;
    private ImageView pdfExportIcon;
    private EditText dayEditText;
    private EditText monthEditText;
    private EditText yearEditText;
    private TextView selectDateIcon;
    private EditText hoursEditText;
    private EditText minutesEditText;
    private TextView selectTimeIcon;
    private CheckBox poopCheckbox;
    private CheckBox peeCheckbox;
    private CheckBox formulaCheckbox;
    private LinearLayout formulaAmountLayout;
    private EditText formulaAmountEditText;
    private CheckBox leftBreastCheckbox;
    private CheckBox rightBreastCheckbox;
    private LinearLayout leftBreastTimeLayout;
    private LinearLayout rightBreastTimeLayout;
    private EditText leftBreastTimeEditText;
    private EditText rightBreastTimeEditText;
    private TextView leftBreastTimeDisplay;
    private TextView rightBreastTimeDisplay;
    private CheckBox burpingCheckbox;
    private EditText descriptionEditText;
    private CheckBox nursingNotificationCheckbox;
    private Button saveButton;

    // Data
    private Calendar selectedDateTime;
    private SimpleDateFormat timeFormat;
    private SimpleDateFormat dateFormat;

    // Notification settings
    private int notificationIntervalMinutes = 180; // Default 3 hours
    private ActivityResultLauncher<Intent> notificationSettingsLauncher;
    private NotificationManager notificationManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Hide action bar
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        setContentView(R.layout.activity_add_entry);

        // Initialize notification manager
        notificationManager = new NotificationManager(this);

        initializeNotificationSettingsLauncher();
        initializeComponents();
        setupClickListeners();
        updateTimeDisplays();
        loadNotificationSettings();
    }

    /**
     * Initialize all UI components and data objects
     */
    private void initializeComponents() {
        // Initialize time objects
        timeFormat = new SimpleDateFormat(TIME_FORMAT_PATTERN, Locale.getDefault());
        dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
        selectedDateTime = Calendar.getInstance();

        // Initialize views
        backButton = findViewById(R.id.backButton);
        pdfExportIcon = findViewById(R.id.pdfExportIcon);
        dayEditText = findViewById(R.id.dayEditText);
        monthEditText = findViewById(R.id.monthEditText);
        yearEditText = findViewById(R.id.yearEditText);
        selectDateIcon = findViewById(R.id.selectDateIcon);
        hoursEditText = findViewById(R.id.hoursEditText);
        minutesEditText = findViewById(R.id.minutesEditText);
        selectTimeIcon = findViewById(R.id.selectTimeIcon);
        poopCheckbox = findViewById(R.id.poopCheckbox);
        peeCheckbox = findViewById(R.id.peeCheckbox);
        formulaCheckbox = findViewById(R.id.formulaCheckbox);
        formulaAmountLayout = findViewById(R.id.formulaAmountLayout);
        formulaAmountEditText = findViewById(R.id.formulaAmountEditText);
        leftBreastCheckbox = findViewById(R.id.leftBreastCheckbox);
        rightBreastCheckbox = findViewById(R.id.rightBreastCheckbox);
        leftBreastTimeLayout = findViewById(R.id.leftBreastTimeLayout);
        rightBreastTimeLayout = findViewById(R.id.rightBreastTimeLayout);
        leftBreastTimeEditText = findViewById(R.id.leftBreastTimeEditText);
        rightBreastTimeEditText = findViewById(R.id.rightBreastTimeEditText);
        leftBreastTimeDisplay = findViewById(R.id.leftBreastTimeDisplay);
        rightBreastTimeDisplay = findViewById(R.id.rightBreastTimeDisplay);
        burpingCheckbox = findViewById(R.id.burpingCheckbox);
        descriptionEditText = findViewById(R.id.descriptionEditText);
        nursingNotificationCheckbox = findViewById(R.id.nursingNotificationCheckbox);
        saveButton = findViewById(R.id.saveButton);
    }

    /**
     * Set up click listeners for all interactive elements
     */
    private void setupClickListeners() {
        // Back button
        backButton.setOnClickListener(v -> finish());

        // PDF Export button
        pdfExportIcon.setOnClickListener(v -> showPDFExportDialog());

        // Date selection
        selectDateIcon.setOnClickListener(v -> showDatePicker());

        // Date input fields - add TextWatchers for manual editing
        setupDateInputWatchers();

        // Time selection via icon
        selectTimeIcon.setOnClickListener(v -> showTimePicker());

        // Formula checkbox - show/hide amount input layout
        formulaCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            formulaAmountLayout.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            if (!isChecked) {
                formulaAmountEditText.setText("");
            }
        });

        // Left breast checkbox - show/hide time selection
        leftBreastCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            leftBreastTimeLayout.setVisibility(isChecked ? View.VISIBLE : View.GONE);
        });

        // Right breast checkbox - show/hide time selection
        rightBreastCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            rightBreastTimeLayout.setVisibility(isChecked ? View.VISIBLE : View.GONE);
        });

        // Left breast time input - add TextWatcher for automatic conversion
        leftBreastTimeEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                updateNursingTimeDisplay(s.toString(), leftBreastTimeDisplay);
            }
        });

        // Right breast time input - add TextWatcher for automatic conversion
        rightBreastTimeEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                updateNursingTimeDisplay(s.toString(), rightBreastTimeDisplay);
            }
        });

        // Add focus change listeners for color transition animation
        setupFocusAnimations();

        // Save button
        saveButton.setOnClickListener(v -> saveEntry());
    }

    /**
     * Show time picker dialog
     */
    private void showTimePicker() {
        int hour = selectedDateTime.get(Calendar.HOUR_OF_DAY);
        int minute = selectedDateTime.get(Calendar.MINUTE);

        TimePickerDialog timePickerDialog = new TimePickerDialog(
            this,
            this,
            hour,
            minute,
            true // 24-hour format
        );
        timePickerDialog.show();
    }

    /**
     * Handle time selection from TimePickerDialog
     */
    @Override
    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
        selectedDateTime.set(Calendar.HOUR_OF_DAY, hourOfDay);
        selectedDateTime.set(Calendar.MINUTE, minute);
        updateTimeDisplays();
    }

    /**
     * Handle date selection from DatePickerDialog
     */
    @Override
    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
        selectedDateTime.set(Calendar.YEAR, year);
        selectedDateTime.set(Calendar.MONTH, month);
        selectedDateTime.set(Calendar.DAY_OF_MONTH, dayOfMonth);
        updateTimeDisplays();
    }

    /**
     * Update date and time display texts
     */
    private void updateTimeDisplays() {
        // Update separate date fields
        int day = selectedDateTime.get(Calendar.DAY_OF_MONTH);
        int month = selectedDateTime.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based
        int year = selectedDateTime.get(Calendar.YEAR);

        dayEditText.setText(String.format(Locale.getDefault(), "%02d", day));
        monthEditText.setText(String.format(Locale.getDefault(), "%02d", month));
        yearEditText.setText(String.valueOf(year));

        // Update separate hours and minutes fields
        int hours = selectedDateTime.get(Calendar.HOUR_OF_DAY);
        int minutes = selectedDateTime.get(Calendar.MINUTE);

        hoursEditText.setText(String.format(Locale.getDefault(), "%02d", hours));
        minutesEditText.setText(String.format(Locale.getDefault(), "%02d", minutes));
    }

    /**
     * Show DatePickerDialog for date selection
     */
    private void showDatePicker() {
        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                this,
                selectedDateTime.get(Calendar.YEAR),
                selectedDateTime.get(Calendar.MONTH),
                selectedDateTime.get(Calendar.DAY_OF_MONTH)
        );
        datePickerDialog.show();
    }

    /**
     * Update nursing time display based on minutes input
     * Converts minutes to hours:minutes format and shows/hides display
     */
    private void updateNursingTimeDisplay(String minutesText, TextView displayView) {
        if (minutesText == null || minutesText.trim().isEmpty()) {
            displayView.setVisibility(View.GONE);
            displayView.setText("");
            return;
        }

        try {
            int minutes = Integer.parseInt(minutesText.trim());
            if (minutes <= 0) {
                displayView.setVisibility(View.GONE);
                displayView.setText("");
                return;
            }

            String formattedTime = formatMinutesToTime(minutes);
            displayView.setText(formattedTime);
            displayView.setVisibility(View.VISIBLE);
        } catch (NumberFormatException e) {
            displayView.setVisibility(View.GONE);
            displayView.setText("");
        }
    }

    /**
     * Convert minutes to hours:minutes format with clear labels
     * Examples: 45 -> "45m", 75 -> "1h 15m", 120 -> "2h"
     */
    private String formatMinutesToTime(int totalMinutes) {
        if (totalMinutes < 60) {
            return totalMinutes + getString(R.string.minutes_short);
        } else {
            int hours = totalMinutes / 60;
            int minutes = totalMinutes % 60;
            if (minutes == 0) {
                return hours + getString(R.string.hours_short);
            } else {
                return hours + getString(R.string.hours_short) + " " + minutes + getString(R.string.minutes_short);
            }
        }
    }

    /**
     * Get nursing time from input field and convert to display format
     * Returns formatted time string for saving to database
     */
    private String getNursingTimeFromInput(EditText inputField) {
        String minutesText = inputField.getText().toString().trim();
        if (minutesText.isEmpty()) {
            return "";
        }

        try {
            int minutes = Integer.parseInt(minutesText);
            if (minutes <= 0) {
                return "";
            }
            return formatMinutesToTime(minutes);
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * Setup focus change animations for nursing time input fields
     * Implements color transition animation for better UX
     */
    private void setupFocusAnimations() {
        // Get colors for animation
        int lightGray = getResources().getColor(R.color.light_gray, null);
        int mediumGray = getResources().getColor(R.color.medium_gray, null);

        // Left breast input focus animation
        leftBreastTimeEditText.setOnFocusChangeListener((v, hasFocus) -> {
            animateHintColor(leftBreastTimeEditText, hasFocus, lightGray, mediumGray);
        });

        // Right breast input focus animation
        rightBreastTimeEditText.setOnFocusChangeListener((v, hasFocus) -> {
            animateHintColor(rightBreastTimeEditText, hasFocus, lightGray, mediumGray);
        });

        // Formula input focus animation
        formulaAmountEditText.setOnFocusChangeListener((v, hasFocus) -> {
            animateHintColor(formulaAmountEditText, hasFocus, lightGray, mediumGray);
        });

        // Hours input focus animation and validation
        hoursEditText.setOnFocusChangeListener((v, hasFocus) -> {
            animateHintColor(hoursEditText, hasFocus, lightGray, mediumGray);
            if (!hasFocus) {
                validateAndUpdateTime();
            }
        });

        // Minutes input focus animation and validation
        minutesEditText.setOnFocusChangeListener((v, hasFocus) -> {
            animateHintColor(minutesEditText, hasFocus, lightGray, mediumGray);
            if (!hasFocus) {
                validateAndUpdateTime();
            }
        });

        // Add TextWatchers for auto-focus and validation
        setupTimeInputWatchers();
    }

    /**
     * Setup TextWatchers for time input fields
     */
    private void setupTimeInputWatchers() {
        // Hours TextWatcher - auto-focus to minutes when 2 digits entered
        hoursEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() == 2) {
                    minutesEditText.requestFocus();
                }
            }
        });

        // Minutes TextWatcher - validate and update time
        minutesEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                // Validation will be handled on focus change
            }
        });
    }

    /**
     * Setup TextWatchers for date input fields
     */
    private void setupDateInputWatchers() {
        // Day TextWatcher - auto-focus to month when 2 digits entered
        dayEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() == 2) {
                    monthEditText.requestFocus();
                }
            }
        });

        // Month TextWatcher - auto-focus to year when 2 digits entered
        monthEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() == 2) {
                    yearEditText.requestFocus();
                }
            }
        });

        // Year TextWatcher - validate and update date when 4 digits entered
        yearEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() == 4) {
                    validateAndUpdateDate();
                }
            }
        });

        // Add focus change listeners for date fields
        dayEditText.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateAndUpdateDate();
            }
        });

        monthEditText.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateAndUpdateDate();
            }
        });

        yearEditText.setOnFocusChangeListener((v, hasFocus) -> {
            if (!hasFocus) {
                validateAndUpdateDate();
            }
        });
    }

    /**
     * Validate and update time from input fields
     */
    private void validateAndUpdateTime() {
        try {
            String hoursText = hoursEditText.getText().toString().trim();
            String minutesText = minutesEditText.getText().toString().trim();

            if (!hoursText.isEmpty() && !minutesText.isEmpty()) {
                int hours = Integer.parseInt(hoursText);
                int minutes = Integer.parseInt(minutesText);

                // Validate ranges
                if (hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                    selectedDateTime.set(Calendar.HOUR_OF_DAY, hours);
                    selectedDateTime.set(Calendar.MINUTE, minutes);
                }
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }

    /**
     * Validate and update date from input fields
     */
    private void validateAndUpdateDate() {
        try {
            String dayText = dayEditText.getText().toString().trim();
            String monthText = monthEditText.getText().toString().trim();
            String yearText = yearEditText.getText().toString().trim();

            if (!dayText.isEmpty() && !monthText.isEmpty() && !yearText.isEmpty()) {
                int day = Integer.parseInt(dayText);
                int month = Integer.parseInt(monthText);
                int year = Integer.parseInt(yearText);

                // Validate ranges
                if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900 && year <= 2100) {
                    // Check if the date is valid (e.g., not Feb 30)
                    Calendar tempCalendar = Calendar.getInstance();
                    tempCalendar.setLenient(false); // Strict date validation
                    try {
                        tempCalendar.set(year, month - 1, day); // month is 0-based in Calendar
                        tempCalendar.getTime(); // This will throw exception if date is invalid

                        // If we get here, date is valid
                        selectedDateTime.set(Calendar.YEAR, year);
                        selectedDateTime.set(Calendar.MONTH, month - 1); // Calendar.MONTH is 0-based
                        selectedDateTime.set(Calendar.DAY_OF_MONTH, day);
                    } catch (Exception e) {
                        // Invalid date (e.g., Feb 30), ignore
                    }
                }
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }

    /**
     * Animate hint text color transition
     */
    private void animateHintColor(EditText editText, boolean hasFocus, int lightColor, int mediumColor) {
        int fromColor = hasFocus ? lightColor : mediumColor;
        int toColor = hasFocus ? mediumColor : lightColor;

        ValueAnimator colorAnimator = ValueAnimator.ofObject(new ArgbEvaluator(), fromColor, toColor);
        colorAnimator.setDuration(200); // 200ms animation
        colorAnimator.addUpdateListener(animator -> {
            editText.setHintTextColor((Integer) animator.getAnimatedValue());
        });
        colorAnimator.start();
    }

    /**
     * Save the entry with all collected data
     */
    private void saveEntry() {
        // Ensure latest time values from input fields are captured
        validateAndUpdateTime();

        // Collect all data
        String time = timeFormat.format(selectedDateTime.getTime());
        boolean poop = poopCheckbox.isChecked();
        boolean pee = peeCheckbox.isChecked();
        boolean formula = formulaCheckbox.isChecked();
        int formulaAmount = 0;
        if (formula) {
            String amountText = formulaAmountEditText.getText().toString().trim();
            if (!amountText.isEmpty()) {
                try {
                    formulaAmount = Integer.parseInt(amountText);
                } catch (NumberFormatException e) {
                    formulaAmount = 0;
                }
            }
        }
        boolean leftBreast = leftBreastCheckbox.isChecked();
        boolean rightBreast = rightBreastCheckbox.isChecked();
        String leftTime = leftBreast ? getNursingTimeFromInput(leftBreastTimeEditText) : "";
        String rightTime = rightBreast ? getNursingTimeFromInput(rightBreastTimeEditText) : "";
        boolean burping = burpingCheckbox.isChecked();
        String description = descriptionEditText.getText().toString().trim();

        // Create new BabyCareEntry
        BabyCareEntry newEntry = new BabyCareEntry(
            time, poop, pee, formula, formulaAmount, leftBreast, rightBreast, leftTime, rightTime, burping, description
        );

        // Set the actual selected date and time as timestamp
        newEntry.setTimestamp(selectedDateTime.getTime());

        // Get notification settings from UI
        boolean notificationEnabled = nursingNotificationCheckbox.isChecked();

        // IMPORTANT: Save notification settings FIRST before calling recordEntry()
        // This ensures isNotificationEnabled() reads the correct value
        notificationManager.saveNotificationSettings(notificationEnabled, notificationIntervalMinutes);

        // Record entry for notification scheduling (any type of entry, not just nursing)
        // recordEntry() will call isNotificationEnabled() which now has the correct value
        if (notificationEnabled) {
            notificationManager.recordEntry(newEntry);
        }

        // Return the entry to MainActivity
        Intent resultIntent = new Intent();
        resultIntent.putExtra("new_entry", newEntry);
        setResult(RESULT_OK, resultIntent);

        // Log confirmation with notification status
        String confirmationMessage = getString(R.string.entry_saved_success_simple);
        if (notificationEnabled) {
            confirmationMessage += " " + getString(R.string.notification_scheduled_for, formatInterval(notificationIntervalMinutes));
        }
        Log.d("AddEntryActivity", confirmationMessage);

        finish();
    }

    /**
     * Format interval minutes into human-readable string
     */
    private String formatInterval(int intervalMinutes) {
        if (intervalMinutes < 60) {
            return getString(R.string.minutes_plural, intervalMinutes);
        } else {
            int hours = intervalMinutes / 60;
            int minutes = intervalMinutes % 60;

            if (minutes == 0) {
                return hours == 1 ? getString(R.string.hour_singular) : getString(R.string.hours_plural, hours);
            } else {
                return getString(R.string.hours_minutes_format, hours, minutes);
            }
        }
    }

    /**
     * Initialize the notification settings activity launcher
     */
    private void initializeNotificationSettingsLauncher() {
        notificationSettingsLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        int intervalMinutes = result.getData().getIntExtra("interval_minutes", 180);
                        boolean vibrationEnabled = result.getData().getBooleanExtra("vibration_enabled", true);
                        boolean soundEnabled = result.getData().getBooleanExtra("sound_enabled", true);

                        notificationIntervalMinutes = intervalMinutes;
                        notificationManager.saveNotificationInterval(intervalMinutes);  // ✅ DODANO!
                        notificationManager.saveVibrationSetting(vibrationEnabled);
                        notificationManager.saveSoundSetting(soundEnabled);

                        Log.d("AddEntryActivity", "Notification settings updated: interval=" + intervalMinutes + " minutes, vibration=" + vibrationEnabled + ", sound=" + soundEnabled);
                    }
                }
            }
        );
    }

    /**
     * Open notification settings activity
     */
    private void openNotificationSettings() {
        Intent intent = new Intent(this, NotificationSettingsActivity.class);
        intent.putExtra("current_interval_minutes", notificationIntervalMinutes);
        intent.putExtra("vibration_enabled", notificationManager.isVibrationEnabled());
        intent.putExtra("sound_enabled", notificationManager.isSoundEnabled());
        notificationSettingsLauncher.launch(intent);
    }



    /**
     * Load existing notification settings
     */
    private void loadNotificationSettings() {
        if (notificationManager != null) {
            boolean enabled = notificationManager.isNotificationEnabled();
            int interval = notificationManager.getNotificationInterval();

            nursingNotificationCheckbox.setChecked(enabled);
            notificationIntervalMinutes = interval;
        }
    }

    /**
     * Show PDF export dialog
     */
    private void showPDFExportDialog() {
        try {
            PDFExportDialog dialog = new PDFExportDialog();
            dialog.setPDFExportListener(this);
            dialog.show(getSupportFragmentManager(), "PDFExportDialog");
            Log.d(TAG, "PDF Export dialog shown");
        } catch (Exception e) {
            Log.e(TAG, "Error showing PDF export dialog", e);
        }
    }

    /**
     * Handle PDF export request from dialog
     * Generates PDF using PDFGenerator and shows result to user
     */
    @Override
    public void onPDFExportRequested(java.util.List<String> selectedColumns, int numberOfRows, boolean isLandscape, boolean includeCheckboxes) {
        Log.d(TAG, "PDF Export requested - Columns: " + selectedColumns + ", Rows: " + numberOfRows + ", Landscape: " + isLandscape + ", Include Checkboxes: " + includeCheckboxes);

        try {
            // Show progress message
            android.widget.Toast.makeText(this, getString(R.string.generating_pdf), android.widget.Toast.LENGTH_SHORT).show();

            // Generate PDF using PDFGenerator
            String pdfPath = PDFGenerator.generateBabyCareTable(this, selectedColumns, numberOfRows, isLandscape, includeCheckboxes);

            if (pdfPath != null) {
                // Success - show file location
                Log.d(TAG, "PDF generated successfully: " + pdfPath);
                showPDFGenerationSuccess(pdfPath);
            } else {
                // Failed - show error
                Log.e(TAG, "PDF generation failed");
                showPDFGenerationError(getString(R.string.pdf_generation_failed));
            }

        } catch (Exception e) {
            Log.e(TAG, "Error during PDF generation", e);
            showPDFGenerationError(getString(R.string.pdf_generation_error_with_message, e.getMessage()));
        }
    }

    /**
     * Show success message with PDF file location
     */
    private void showPDFGenerationSuccess(String pdfPath) {
        String message = getString(R.string.pdf_generated_success, pdfPath);
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_LONG).show();
    }

    /**
     * Show error message for PDF generation failure
     */
    private void showPDFGenerationError(String errorMessage) {
        android.widget.Toast.makeText(this, errorMessage, android.widget.Toast.LENGTH_LONG).show();
    }
}
