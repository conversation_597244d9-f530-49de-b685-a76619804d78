package com.example.babyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

/**
 * Tracks missed notifications and manages notification state
 * Helps determine when to show missed notification pop-up to user
 */
public class MissedNotificationTracker {

    private static final String TAG = "MissedNotificationTracker";
    private static final String PREFS_NAME = "missed_notifications";
    private static final String KEY_HAS_MISSED_NOTIFICATIONS = "has_missed_notifications";
    private static final String KEY_LAST_NOTIFICATION_TIME = "last_notification_time";
    private static final String KEY_LAST_APP_OPEN_TIME = "last_app_open_time";
    private static final String KEY_NOTIFICATION_COUNT = "notification_count";
    private static final String KEY_POPUP_SHOWN_FOR_NOTIFICATION = "popup_shown_for_notification";

    private Context context;
    private SharedPreferences prefs;

    public MissedNotificationTracker(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null in MissedNotificationTracker constructor");
            throw new IllegalArgumentException("Context cannot be null");
        }

        try {
            this.context = context.getApplicationContext(); // Use application context to avoid memory leaks
            this.prefs = this.context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);

            if (this.prefs == null) {
                Log.e(TAG, "Failed to get SharedPreferences");
                throw new RuntimeException("Failed to initialize SharedPreferences");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error initializing MissedNotificationTracker", e);
            throw e;
        }
    }

    /**
     * Record that a notification was sent
     * @param notificationTime The time when notification was sent
     */
    public void recordNotificationSent(long notificationTime) {
        try {
            if (prefs == null) {
                Log.e(TAG, "SharedPreferences is null, cannot record notification");
                return;
            }

            if (notificationTime <= 0) {
                Log.w(TAG, "Invalid notification time: " + notificationTime);
                notificationTime = System.currentTimeMillis();
            }

            SharedPreferences.Editor editor = prefs.edit();
            if (editor == null) {
                Log.e(TAG, "Failed to get SharedPreferences editor");
                return;
            }

            editor.putBoolean(KEY_HAS_MISSED_NOTIFICATIONS, true);
            editor.putLong(KEY_LAST_NOTIFICATION_TIME, notificationTime);

            // Increment notification count
            int currentCount = prefs.getInt(KEY_NOTIFICATION_COUNT, 0);
            editor.putInt(KEY_NOTIFICATION_COUNT, currentCount + 1);

            boolean success = editor.commit(); // Use commit for immediate write
            if (!success) {
                Log.e(TAG, "Failed to save notification data to SharedPreferences");
            } else {
                Log.d(TAG, "Notification recorded at: " + new java.util.Date(notificationTime));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error recording notification", e);
        }
    }

    /**
     * Record that the app was opened normally (not from notification)
     * @param openTime The time when app was opened
     */
    public void recordAppOpened(long openTime) {
        try {
            SharedPreferences.Editor editor = prefs.edit();
            editor.putLong(KEY_LAST_APP_OPEN_TIME, openTime);
            editor.apply();

            Log.d(TAG, "App open recorded at: " + new java.util.Date(openTime));
        } catch (Exception e) {
            Log.e(TAG, "Error recording app open", e);
        }
    }

    /**
     * Check if there are missed notifications that user should be informed about
     * @return true if there are missed notifications
     */
    public boolean hasMissedNotifications() {
        try {
            boolean hasMissed = prefs.getBoolean(KEY_HAS_MISSED_NOTIFICATIONS, false);
            long lastNotificationTime = prefs.getLong(KEY_LAST_NOTIFICATION_TIME, 0);
            long lastAppOpenTime = prefs.getLong(KEY_LAST_APP_OPEN_TIME, 0);

            // If no notifications were sent, no missed notifications
            if (!hasMissed || lastNotificationTime == 0) {
                return false;
            }

            // If notification was sent after last app open, it's missed
            boolean isMissed = lastNotificationTime > lastAppOpenTime;

            Log.d(TAG, "Checking missed notifications: hasMissed=" + hasMissed +
                      ", lastNotification=" + new java.util.Date(lastNotificationTime) +
                      ", lastAppOpen=" + new java.util.Date(lastAppOpenTime) +
                      ", isMissed=" + isMissed);

            return isMissed;
        } catch (Exception e) {
            Log.e(TAG, "Error checking missed notifications", e);
            return false;
        }
    }

    /**
     * Get the count of missed notifications
     * @return number of missed notifications
     */
    public int getMissedNotificationCount() {
        try {
            if (!hasMissedNotifications()) {
                return 0;
            }
            return prefs.getInt(KEY_NOTIFICATION_COUNT, 0);
        } catch (Exception e) {
            Log.e(TAG, "Error getting missed notification count", e);
            return 0;
        }
    }

    /**
     * Clear missed notification status (called when user acknowledges)
     */
    public void clearMissedNotifications() {
        try {
            SharedPreferences.Editor editor = prefs.edit();
            editor.putBoolean(KEY_HAS_MISSED_NOTIFICATIONS, false);
            editor.putInt(KEY_NOTIFICATION_COUNT, 0);
            editor.putLong(KEY_LAST_APP_OPEN_TIME, System.currentTimeMillis());
            editor.putLong(KEY_POPUP_SHOWN_FOR_NOTIFICATION, 0); // Reset popup shown flag
            editor.apply();

            Log.d(TAG, "Missed notifications cleared and popup flag reset");
        } catch (Exception e) {
            Log.e(TAG, "Error clearing missed notifications", e);
        }
    }

    /**
     * Get formatted message about missed notifications
     * @return user-friendly message about missed notifications
     */
    public String getMissedNotificationMessage() {
        try {
            int count = getMissedNotificationCount();
            long lastNotificationTime = prefs.getLong(KEY_LAST_NOTIFICATION_TIME, 0);

            if (count <= 0) {
                return "You had a scheduled notification while you were away.";
            } else if (count == 1) {
                return "You had 1 scheduled notification while you were away.";
            } else {
                return "You had " + count + " scheduled notifications while you were away.";
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting missed notification message", e);
            return "You had scheduled notifications while you were away.";
        }
    }

    /**
     * Check if enough time has passed since last notification to show pop-up
     * @return true if pop-up should be shown
     */
    public boolean shouldShowMissedNotificationPopup() {
        try {
            if (!hasMissedNotifications()) {
                return false;
            }

            long lastNotificationTime = prefs.getLong(KEY_LAST_NOTIFICATION_TIME, 0);
            long popupShownForNotification = prefs.getLong(KEY_POPUP_SHOWN_FOR_NOTIFICATION, 0);

            // Check if popup was already shown for this specific notification
            if (popupShownForNotification >= lastNotificationTime) {
                Log.d(TAG, "Popup already shown for this notification - not showing again");
                return false;
            }

            long currentTime = System.currentTimeMillis();

            // Show pop-up if at least 1 minute has passed since notification
            long timeDifference = currentTime - lastNotificationTime;
            boolean shouldShow = timeDifference > (1 * 60 * 1000); // 1 minute

            Log.d(TAG, "Should show popup: " + shouldShow + " (time diff: " + (timeDifference / 1000) + " seconds, popup not shown yet)");

            return shouldShow;
        } catch (Exception e) {
            Log.e(TAG, "Error checking if should show popup", e);
            return false;
        }
    }

    /**
     * Mark that popup was shown for the current notification
     * This prevents showing the same popup multiple times
     */
    public void markPopupShownForCurrentNotification() {
        try {
            long lastNotificationTime = prefs.getLong(KEY_LAST_NOTIFICATION_TIME, 0);
            if (lastNotificationTime > 0) {
                SharedPreferences.Editor editor = prefs.edit();
                editor.putLong(KEY_POPUP_SHOWN_FOR_NOTIFICATION, lastNotificationTime);
                boolean success = editor.commit();

                if (success) {
                    Log.d(TAG, "Marked popup as shown for notification at: " + new java.util.Date(lastNotificationTime));
                } else {
                    Log.e(TAG, "Failed to mark popup as shown");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error marking popup as shown", e);
        }
    }
}
