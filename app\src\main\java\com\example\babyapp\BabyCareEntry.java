package com.example.babyapp;

import android.content.Context;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Data model for baby care entries
 * Contains all information about a single baby care event
 */
public class BabyCareEntry implements Serializable {

    private long id;
    private Date timestamp;
    private String time;
    private boolean poop;
    private boolean pee;
    private boolean formula;
    private int formulaAmount;
    private boolean leftBreast;
    private boolean rightBreast;
    private String leftBreastTime;
    private String rightBreastTime;
    private boolean burping;
    private String description;
    private Date dateCreated;

    // Date formatters
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());

    /**
     * Constructor for creating a new baby care entry
     */
    public BabyCareEntry(String time, boolean poop, boolean pee, boolean formula, int formulaAmount,
                        boolean leftBreast, boolean rightBreast, String leftBreastTime,
                        String rightBreastTime, boolean burping, String description) {
        this.id = System.currentTimeMillis(); // Simple ID generation
        this.timestamp = new Date();
        this.time = time;
        this.poop = poop;
        this.pee = pee;
        this.formula = formula;
        this.formulaAmount = formulaAmount;
        this.leftBreast = leftBreast;
        this.rightBreast = rightBreast;
        this.leftBreastTime = leftBreastTime;
        this.rightBreastTime = rightBreastTime;
        this.burping = burping;
        this.description = description;
        this.dateCreated = new Date();
    }

    // Getters
    public long getId() { return id; }
    public Date getTimestamp() { return timestamp; }
    public String getTime() { return time; }
    public boolean isPoop() { return poop; }
    public boolean isPee() { return pee; }
    public boolean isFormula() { return formula; }
    public int getFormulaAmount() { return formulaAmount; }
    public boolean isLeftBreast() { return leftBreast; }
    public boolean isRightBreast() { return rightBreast; }
    public String getLeftBreastTime() { return leftBreastTime; }
    public String getRightBreastTime() { return rightBreastTime; }
    public boolean isBurping() { return burping; }
    public String getDescription() { return description; }
    public Date getDateCreated() { return dateCreated; }

    // Setters
    public void setTime(String time) { this.time = time; }
    public void setTimestamp(Date timestamp) { this.timestamp = timestamp; }
    public void setPoop(boolean poop) { this.poop = poop; }
    public void setPee(boolean pee) { this.pee = pee; }
    public void setFormula(boolean formula) { this.formula = formula; }
    public void setFormulaAmount(int formulaAmount) { this.formulaAmount = formulaAmount; }
    public void setLeftBreast(boolean leftBreast) { this.leftBreast = leftBreast; }
    public void setRightBreast(boolean rightBreast) { this.rightBreast = rightBreast; }
    public void setLeftBreastTime(String leftBreastTime) { this.leftBreastTime = leftBreastTime; }
    public void setRightBreastTime(String rightBreastTime) { this.rightBreastTime = rightBreastTime; }
    public void setBurping(boolean burping) { this.burping = burping; }
    public void setDescription(String description) { this.description = description; }
    public void setDateCreated(Date dateCreated) { this.dateCreated = dateCreated; }

    /**
     * Get formatted date string
     */
    public String getFormattedDate() {
        return DATE_FORMAT.format(timestamp);
    }

    /**
     * Get formatted time string using user's preferred time format
     */
    public String getFormattedTime() {
        return time; // Time is already formatted when saved
    }

    /**
     * Get formatted time string with context for time format preference
     */
    public String getFormattedTime(Context context) {
        if (time != null) {
            return time; // Use saved formatted time
        }
        // Fallback: format timestamp using user's preference
        TimeFormatManager timeFormatManager = new TimeFormatManager(context);
        return timeFormatManager.formatTime(timestamp);
    }

    /**
     * Get summary of activities for display
     */
    public String getActivitySummary() {
        // Fallback method for backward compatibility - uses English labels
        StringBuilder summary = new StringBuilder();

        if (poop || pee) {
            summary.append("Diaper: ");
            if (poop) summary.append("💩 ");
            if (pee) summary.append("💧 ");
        }

        if (formula) {
            if (summary.length() > 0) summary.append(" • ");
            summary.append("Formula: 🍼 ").append(formulaAmount).append("ml");
        }

        if (leftBreast || rightBreast) {
            if (summary.length() > 0) summary.append(" • ");
            summary.append("Nursing: ");
            if (leftBreast) summary.append("L(").append(leftBreastTime).append(") ");
            if (rightBreast) summary.append("R(").append(rightBreastTime).append(") ");
        }

        if (burping) {
            if (summary.length() > 0) summary.append(" • ");
            summary.append("Burping: 🤱");
        }

        return summary.toString().trim();
    }

    /**
     * Get localized summary of activities for display
     */
    public String getActivitySummary(Context context) {
        StringBuilder summary = new StringBuilder();

        if (poop || pee) {
            summary.append(context.getString(R.string.activity_diaper_label)).append(" ");
            if (poop) summary.append("💩 ");
            if (pee) summary.append("💧 ");
        }

        if (formula) {
            if (summary.length() > 0) summary.append(" • ");
            summary.append(context.getString(R.string.activity_formula_label)).append(" 🍼 ").append(formulaAmount).append("ml");
        }

        if (leftBreast || rightBreast) {
            if (summary.length() > 0) summary.append(" • ");
            summary.append(context.getString(R.string.activity_nursing_label)).append(" ");
            if (leftBreast) summary.append("L(").append(leftBreastTime).append(") ");
            if (rightBreast) summary.append("R(").append(rightBreastTime).append(") ");
        }

        if (burping) {
            if (summary.length() > 0) summary.append(" • ");
            summary.append(context.getString(R.string.activity_burping_label)).append(" 🤱");
        }

        return summary.toString().trim();
    }

    /**
     * Get short description for list display
     */
    public String getShortDescription() {
        if (description == null || description.trim().isEmpty()) {
            return "No description";
        }

        String trimmed = description.trim();
        if (trimmed.length() <= 50) {
            return trimmed;
        }

        return trimmed.substring(0, 47) + "...";
    }

    /**
     * Get localized short description for list display
     */
    public String getShortDescription(Context context) {
        if (description == null || description.trim().isEmpty()) {
            return context.getString(R.string.activity_no_description);
        }

        String trimmed = description.trim();
        if (trimmed.length() <= 50) {
            return trimmed;
        }

        return trimmed.substring(0, 47) + "...";
    }

    @Override
    public String toString() {
        return "BabyCareEntry{" +
                "id=" + id +
                ", time='" + time + '\'' +
                ", activities='" + getActivitySummary() + '\'' +
                ", description='" + getShortDescription() + '\'' +
                '}';
    }
}
