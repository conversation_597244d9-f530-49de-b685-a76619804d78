<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_blue"
    android:orientation="vertical">

    <!-- Top Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/baby_pink"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/backButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:contentDescription="@string/back_button"
            android:focusable="true"
            android:padding="8dp"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/white" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/notification_settings_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Spacer for balance -->
        <View
            android:layout_width="40dp"
            android:layout_height="40dp" />

    </LinearLayout>

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Description -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="@string/notification_settings_description"
                android:textColor="@color/dark_blue"
                android:textSize="14sp"
                android:lineSpacingExtra="4dp" />

            <!-- Time Interval Selection -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/notification_interval_label"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Predefined Time Options -->
            <RadioGroup
                android:id="@+id/timeIntervalRadioGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/radio1Hour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/time_1_hour"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink" />

                <RadioButton
                    android:id="@+id/radio2Hours"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/time_2_hours"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink" />

                <RadioButton
                    android:id="@+id/radio3Hours"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:checked="true"
                    android:text="@string/time_3_hours"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink" />

                <RadioButton
                    android:id="@+id/radio4Hours"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/time_4_hours"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink" />

                <RadioButton
                    android:id="@+id/radio6Hours"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="@string/time_6_hours"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink" />

                <RadioButton
                    android:id="@+id/radioCustom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/time_custom"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp"
                    android:buttonTint="@color/baby_pink" />

            </RadioGroup>

            <!-- Custom Time Input -->
            <LinearLayout
                android:id="@+id/customTimeLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="@string/custom_time_label"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/customHoursEditText"
                    android:layout_width="60dp"
                    android:layout_height="48dp"
                    android:background="@drawable/edittext_background"
                    android:gravity="center"
                    android:hint="@string/time_hint"
                    android:inputType="number"
                    android:maxLength="2"
                    android:textColor="@color/dark_blue"
                    android:textColorHint="@color/gray"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="8dp"
                    android:text="@string/hours_label"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

                <EditText
                    android:id="@+id/customMinutesEditText"
                    android:layout_width="60dp"
                    android:layout_height="48dp"
                    android:background="@drawable/edittext_background"
                    android:gravity="center"
                    android:hint="@string/time_hint"
                    android:inputType="number"
                    android:maxLength="2"
                    android:textColor="@color/dark_blue"
                    android:textColorHint="@color/gray"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/minutes_label"
                    android:textColor="@color/dark_blue"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Preview -->
            <TextView
                android:id="@+id/previewText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:background="@drawable/filter_label_background"
                android:padding="12dp"
                android:text="@string/preview_3_hours"
                android:textColor="@color/dark_blue"
                android:textSize="12sp"
                android:textStyle="italic" />

            <!-- Vibration Settings Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/notification_feedback_label"
                android:textColor="@color/dark_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Vibration Toggle -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/rounded_background_white"
                android:padding="16dp"
                android:elevation="2dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/vibration_label"
                        android:textColor="@color/dark_blue"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="@string/vibration_description"
                        android:textColor="@color/gray"
                        android:textSize="12sp"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/vibrationSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:checked="true"
                    android:thumbTint="@color/baby_pink"
                    android:trackTint="@color/light_gray" />

            </LinearLayout>

            <!-- Sound Toggle -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/rounded_background_white"
                android:padding="16dp"
                android:elevation="2dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/sound_label"
                        android:textColor="@color/dark_blue"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="@string/sound_description"
                        android:textColor="@color/gray"
                        android:textSize="12sp"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>

                <Switch
                    android:id="@+id/soundSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:checked="true"
                    android:thumbTint="@color/baby_pink"
                    android:trackTint="@color/light_gray" />

            </LinearLayout>

            <!-- Notification Info -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:text="@string/feedback_tip"
                android:textColor="@color/gray"
                android:textSize="11sp"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </ScrollView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:backgroundTint="@color/gray"
            android:text="@string/cancel_button"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/saveButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:backgroundTint="@color/baby_pink"
            android:text="@string/save_button"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>
