<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white">

    <!-- Dialog Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/pdf_export_title"
        android:textColor="@color/dark_blue"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Columns Section -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_columns_title"
        android:textColor="@color/dark_blue"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <!-- Time Column (Always checked) -->
    <CheckBox
        android:id="@+id/timeColumnCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_time_column"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:checked="true"
        android:enabled="false"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="8dp" />

    <!-- Poop Column -->
    <CheckBox
        android:id="@+id/poopColumnCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_poop_column"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="8dp" />

    <!-- Pee Column -->
    <CheckBox
        android:id="@+id/peeColumnCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_pee_column"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="8dp" />

    <!-- Formula Column -->
    <CheckBox
        android:id="@+id/formulaColumnCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_formula_column"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="8dp" />

    <!-- Nursing Column -->
    <CheckBox
        android:id="@+id/nursingColumnCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_nursing_column"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="8dp" />

    <!-- Burping Column -->
    <CheckBox
        android:id="@+id/burpingColumnCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_burping_column"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="8dp" />

    <!-- Notes Column -->
    <CheckBox
        android:id="@+id/notesColumnCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_notes_column"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="16dp" />

    <!-- Rows Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pdf_rows_label"
            android:textColor="@color/dark_blue"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/rowsEditText"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:background="@drawable/edittext_background"
            android:hint="@string/pdf_rows_hint"
            android:inputType="number"
            android:maxLength="3"
            android:padding="12dp"
            android:textColor="@color/dark_blue"
            android:textColorHint="@color/gray"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- Page Orientation Section -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_orientation_label"
        android:textColor="@color/dark_blue"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <RadioGroup
        android:id="@+id/orientationRadioGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">

        <RadioButton
            android:id="@+id/portraitRadio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pdf_portrait"
            android:textColor="@color/dark_blue"
            android:textSize="14sp"
            android:checked="true"
            android:buttonTint="@color/baby_pink"
            android:layout_marginEnd="24dp" />

        <RadioButton
            android:id="@+id/landscapeRadio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pdf_landscape"
            android:textColor="@color/dark_blue"
            android:textSize="14sp"
            android:buttonTint="@color/baby_pink" />

    </RadioGroup>

    <!-- Include Checkboxes Section -->
    <CheckBox
        android:id="@+id/includeCheckboxesCheckbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pdf_include_checkboxes"
        android:textColor="@color/dark_blue"
        android:textSize="14sp"
        android:checked="true"
        android:buttonTint="@color/baby_pink"
        android:button="@drawable/checkbox_selector"
        android:layout_marginBottom="24dp" />

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/pdf_cancel_button"
            android:textColor="@color/gray"
            android:background="@android:color/transparent" />

        <View
            android:layout_width="24dp"
            android:layout_height="1dp" />

        <Button
            android:id="@+id/generatePdfButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/pdf_generate_button"
            android:textColor="@color/white"
            android:backgroundTint="@color/baby_pink" />

    </LinearLayout>

</LinearLayout>
