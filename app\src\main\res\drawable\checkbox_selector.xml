<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Checked state - baby pink background with white checkmark -->
    <item android:state_checked="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/baby_pink" />
                    <corners android:radius="3dp" />
                    <stroke android:width="2dp" android:color="@color/baby_pink" />
                </shape>
            </item>
            <item android:gravity="center">
                <vector android:width="12dp" android:height="12dp" android:viewportWidth="24" android:viewportHeight="24">
                    <path android:fillColor="@color/white" android:pathData="M9,16.17L4.83,12l-1.42,1.41L9,19 21,7l-1.41,-1.41z"/>
                </vector>
            </item>
        </layer-list>
    </item>
    <!-- Unchecked state - transparent with gray border -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="3dp" />
            <stroke android:width="2dp" android:color="@color/gray" />
        </shape>
    </item>
</selector>
